import { motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { invoiceGeneratorAPI } from "../../../../services/api";

const CompanyInfoEditor = ({ selectedTemplate, onSave, onBack, variants }) => {
  const [companyInfo, setCompanyInfo] = useState({
    company_name: '',
    address_line_1: '',
    address_line_2: '',
    city: '',
    state_province: '',
    postal_code: '',
    country: '',
    phone: '',
    email: '',
    website: '',
    tax_id: '',
    business_registration: '',
    bank_name: '',
    account_number: '',
    routing_number: '',
    swift_code: '',
    default_payment_terms: 'Net 30 days',
    logo: null
  });
  
  const [logoPreview, setLogoPreview] = useState(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");
  const logoInputRef = useRef(null);

  useEffect(() => {
    loadCompanyProfile();
  }, []);

  const loadCompanyProfile = async () => {
    try {
      setLoading(true);
      const response = await invoiceGeneratorAPI.getCompanyProfile();
      if (response.data && Object.keys(response.data).length > 0) {
        setCompanyInfo(response.data);
        if (response.data.logo) {
          setLogoPreview(response.data.logo);
        }
      }
    } catch (err) {
      console.error('Error loading company profile:', err);
      // Don't show error for empty profile, it's expected for new users
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setCompanyInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLogoChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        setError("Logo file size must be less than 5MB");
        return;
      }
      
      setCompanyInfo(prev => ({
        ...prev,
        logo: file
      }));
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError("");
      
      // Validate required fields
      if (!companyInfo.company_name.trim()) {
        setError("Company name is required");
        return;
      }
      
      // Create FormData for file upload
      const formData = new FormData();
      Object.keys(companyInfo).forEach(key => {
        if (companyInfo[key] !== null && companyInfo[key] !== '') {
          formData.append(key, companyInfo[key]);
        }
      });
      
      // Save or update company profile
      let response;
      try {
        response = await invoiceGeneratorAPI.updateCompanyProfile(formData);
      } catch (err) {
        if (err.response?.status === 404) {
          response = await invoiceGeneratorAPI.createCompanyProfile(formData);
        } else {
          throw err;
        }
      }
      
      onSave(response.data);
    } catch (err) {
      console.error('Error saving company profile:', err);
      setError("Failed to save company information. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <motion.div
        className="flex flex-col items-center justify-center py-12"
        variants={variants}
      >
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <p className="text-gray-600">Loading company information...</p>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={onBack}
          className="flex items-center text-blue-600 hover:text-blue-800 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back
        </button>
        <h2 className="text-xl font-medium text-gray-700">
          Customize Company Information
        </h2>
        <div className="w-20"></div>
      </div>

      <p className="text-gray-600 text-center max-w-2xl mx-auto">
        Enter your company information to customize the <strong>{selectedTemplate?.name}</strong> template.
      </p>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Company Information Form */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-700 mb-4">Basic Information</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Name *
                </label>
                <input
                  type="text"
                  value={companyInfo.company_name}
                  onChange={(e) => handleInputChange('company_name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Your Company Name"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 1
                </label>
                <input
                  type="text"
                  value={companyInfo.address_line_1}
                  onChange={(e) => handleInputChange('address_line_1', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Street address"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 2
                </label>
                <input
                  type="text"
                  value={companyInfo.address_line_2}
                  onChange={(e) => handleInputChange('address_line_2', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Apartment, suite, etc."
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    City
                  </label>
                  <input
                    type="text"
                    value={companyInfo.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="City"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    State/Province
                  </label>
                  <input
                    type="text"
                    value={companyInfo.state_province}
                    onChange={(e) => handleInputChange('state_province', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="State/Province"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Postal Code
                  </label>
                  <input
                    type="text"
                    value={companyInfo.postal_code}
                    onChange={(e) => handleInputChange('postal_code', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Postal Code"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Country
                  </label>
                  <input
                    type="text"
                    value={companyInfo.country}
                    onChange={(e) => handleInputChange('country', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Country"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-700 mb-4">Contact Information</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone
                </label>
                <input
                  type="tel"
                  value={companyInfo.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="+****************"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  value={companyInfo.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Website
                </label>
                <input
                  type="url"
                  value={companyInfo.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="https://www.company.com"
                />
              </div>
            </div>
          </div>

          {/* Company Logo */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-700 mb-4">Company Logo</h3>
            
            <div className="space-y-4">
              <div>
                <input
                  type="file"
                  ref={logoInputRef}
                  onChange={handleLogoChange}
                  accept="image/*"
                  className="hidden"
                />
                <button
                  type="button"
                  onClick={() => logoInputRef.current?.click()}
                  className="w-full px-4 py-2 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 transition-colors"
                >
                  <div className="text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mx-auto text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <p className="text-gray-600">Click to upload logo</p>
                    <p className="text-sm text-gray-400">PNG, JPG up to 5MB</p>
                  </div>
                </button>
              </div>
              
              {logoPreview && (
                <div className="flex justify-center">
                  <img
                    src={logoPreview}
                    alt="Logo preview"
                    className="max-h-20 max-w-32 object-contain border border-gray-200 rounded"
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Template Preview */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-700 mb-4">Template Preview</h3>
            
            {/* Mock invoice preview */}
            <div className="border border-gray-200 rounded-lg p-6 bg-gray-50">
              <div className="space-y-4">
                <div className="flex justify-between items-start">
                  <div>
                    {logoPreview && (
                      <img src={logoPreview} alt="Logo" className="h-12 mb-2" />
                    )}
                    <h4 className="font-bold text-lg">
                      {companyInfo.company_name || 'Your Company Name'}
                    </h4>
                    <div className="text-sm text-gray-600">
                      {companyInfo.address_line_1 && <div>{companyInfo.address_line_1}</div>}
                      {companyInfo.address_line_2 && <div>{companyInfo.address_line_2}</div>}
                      {(companyInfo.city || companyInfo.state_province || companyInfo.postal_code) && (
                        <div>
                          {companyInfo.city}{companyInfo.city && companyInfo.state_province && ', '}{companyInfo.state_province} {companyInfo.postal_code}
                        </div>
                      )}
                      {companyInfo.country && <div>{companyInfo.country}</div>}
                      {companyInfo.phone && <div>{companyInfo.phone}</div>}
                      {companyInfo.email && <div>{companyInfo.email}</div>}
                    </div>
                  </div>
                  <div className="text-right">
                    <h4 className="font-bold text-xl">INVOICE</h4>
                    <div className="text-sm text-gray-600">
                      <div>Invoice #: INV-001</div>
                      <div>Date: {new Date().toLocaleDateString()}</div>
                    </div>
                  </div>
                </div>
                
                <div className="border-t border-gray-300 pt-4">
                  <div className="text-sm text-gray-500">
                    This is a preview of how your company information will appear on invoices.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-4 pt-6 border-t border-gray-200">
        <button
          onClick={onBack}
          className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Back
        </button>
        <button
          onClick={handleSave}
          disabled={saving || !companyInfo.company_name.trim()}
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {saving ? 'Saving...' : 'Save & Continue'}
        </button>
      </div>
    </motion.div>
  );
};

export default CompanyInfoEditor;
